<?php
/**
 * Test script to verify the AI integration works properly
 */

// Include necessary files
require_once 'includes/functions.php';
require_once 'config/app.php';

echo "=== AI Integration Test ===\n\n";

// Test 1: Check if Runware is in supported providers
$supportedProviders = Config::get('ai_providers.supported', []);
echo "Supported providers: " . implode(', ', $supportedProviders) . "\n";

if (in_array('runware', $supportedProviders)) {
    echo "✅ Runware is properly configured as a supported provider\n";
} else {
    echo "❌ Runware is NOT configured as a supported provider\n";
}

// Test 2: Check if the controller files exist and are readable
$controllers = [
    'controllers/GenerateController.php',
    'controllers/ApiController.php'
];

foreach ($controllers as $controller) {
    if (file_exists($controller) && is_readable($controller)) {
        echo "✅ $controller exists and is readable\n";
    } else {
        echo "❌ $controller is missing or not readable\n";
    }
}

// Test 3: Check if view files have been updated
$viewFiles = [
    'views/generate/index.php',
    'views/generate/bulk.php'
];

foreach ($viewFiles as $viewFile) {
    if (file_exists($viewFile)) {
        $content = file_get_contents($viewFile);
        if (strpos($content, 'rundiffusion:130@100') !== false) {
            echo "✅ $viewFile has been updated with Runware models\n";
        } else {
            echo "❌ $viewFile does not contain Runware models\n";
        }
    } else {
        echo "❌ $viewFile is missing\n";
    }
}

echo "\n=== Test Complete ===\n";
?>
